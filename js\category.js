// Category Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Get the category from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const category = urlParams.get('cat') || 'general';
    
    // Update the page title and heading
    updateCategoryInfo(category);
    
    // Load articles for the category
    loadCategoryArticles(category);
    
    // Load trending articles
    loadTrendingArticles();
    
    // Set up pagination
    setupPagination();
    
    // Set up event listeners
    setupEventListeners();
});

// Update the category information on the page
function updateCategoryInfo(category) {
    const categoryTitle = document.getElementById('category-title');
    const featuredCategory = document.getElementById('featured-category');
    
    if (!categoryTitle || !featuredCategory) return;
    
    const categoryName = getCategoryName(category);
    categoryTitle.textContent = categoryName;
    featuredCategory.textContent = categoryName;
    
    // Update the active link in the navigation
    document.querySelectorAll('.main-nav a').forEach(link => {
        if (link.textContent.toLowerCase() === categoryName.toLowerCase()) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Get the display name for a category
function getCategoryName(category) {
    const categories = {
        'world': 'World',
        'politics': 'Politics',
        'business': 'Business',
        'technology': 'Technology',
        'sports': 'Sports',
        'entertainment': 'Entertainment',
        'general': 'Latest News'
    };
    
    return categories[category] || 'Latest News';
}

// Load articles for the selected category
function loadCategoryArticles(category) {
    // In a real application, this would be an API call
    // For now, we'll use mock data
    const mockArticles = generateMockArticles(category, 12);
    
    if (mockArticles.length > 0) {
        // Update the featured article
        updateFeaturedArticle(mockArticles[0]);
        
        // Update the articles grid
        updateArticlesGrid(mockArticles.slice(1));
    }
}

// Generate mock articles for demonstration
function generateMockArticles(category, count) {
    const categories = ['World', 'Politics', 'Business', 'Technology', 'Sports', 'Entertainment'];
    const titles = {
        'World': [
            'Global Leaders Meet to Discuss Climate Change',
            'New International Trade Agreement Reached',
            'Humanitarian Crisis Worsens in Conflict Zone'
        ],
        'Politics': [
            'New Legislation Aims to Reform Healthcare System',
            'Election Polls Show Tight Race in Key States',
            'Senate Committee Hears Testimony on Infrastructure Bill'
        ],
        'Business': [
            'Stock Markets Reach Record High',
            'Tech Giant Announces Revolutionary New Product',
            'Startup Secures $50 Million in Series B Funding'
        ],
        'Technology': [
            'Breakthrough in Quantum Computing Announced',
            'New Smartphone Features Impress at Tech Expo',
            'Cybersecurity Threats on the Rise, Experts Warn'
        ],
        'Sports': [
            'Underdog Team Wins Championship in Overtime Thriller',
            'Athlete Breaks World Record at International Event',
            'Controversial Call Decides Playoff Game'
        ],
        'Entertainment': [
            'Highly Anticipated Movie Breaks Box Office Records',
            'Award-Winning Series Announces Final Season',
            'Music Festival Lineup Features Top Artists'
        ]
    };
    
    const authors = ['Jane Smith', 'John Doe', 'Maria Garcia', 'David Kim', 'Sarah Johnson', 'Michael Brown'];
    const articles = [];
    
    // If category is 'general', include articles from all categories
    const selectedCategories = category === 'general' ? categories : [getCategoryName(category)];
    
    for (let i = 0; i < count; i++) {
        const articleCategory = selectedCategories[Math.floor(Math.random() * selectedCategories.length)];
        const categoryTitles = titles[articleCategory] || ['Breaking News Story'];
        const title = categoryTitles[Math.floor(Math.random() * categoryTitles.length)];
        
        articles.push({
            id: `article-${i + 1}`,
            title: title,
            excerpt: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
            category: articleCategory,
            author: authors[Math.floor(Math.random() * authors.length)],
            date: getRandomDate(),
            imageUrl: `https://source.unsplash.com/random/600x400/?${articleCategory.toLowerCase()},${i}`,
            readTime: `${Math.floor(Math.random() * 10) + 3} min read`
        });
    }
    
    return articles;
}

// Generate a random date within the last 30 days
function getRandomDate() {
    const today = new Date();
    const randomDaysAgo = Math.floor(Math.random() * 30);
    const date = new Date(today);
    date.setDate(date.getDate() - randomDaysAgo);
    
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Update the featured article
function updateFeaturedArticle(article) {
    if (!article) return;
    
    const featuredImage = document.getElementById('featured-image');
    const featuredCaption = document.getElementById('featured-caption');
    const featuredTitle = document.getElementById('featured-title');
    const featuredAuthor = document.getElementById('featured-author');
    const featuredDate = document.getElementById('featured-date');
    const featuredExcerpt = document.getElementById('featured-excerpt');
    const featuredCategory = document.getElementById('featured-category');
    
    if (featuredImage) featuredImage.src = article.imageUrl;
    if (featuredImage) featuredImage.alt = article.title;
    if (featuredCaption) featuredCaption.textContent = article.title;
    if (featuredTitle) featuredTitle.textContent = article.title;
    if (featuredAuthor) featuredAuthor.textContent = article.author;
    if (featuredDate) featuredDate.textContent = article.date;
    if (featuredExcerpt) featuredExcerpt.textContent = article.excerpt;
    if (featuredCategory) featuredCategory.textContent = article.category;
    
    // Update the read more link
    const readMoreLink = document.querySelector('.featured-story .read-more');
    if (readMoreLink) {
        readMoreLink.href = `article.html?id=${article.id}`;
    }
}

// Update the articles grid
function updateArticlesGrid(articles) {
    const grid = document.getElementById('articles-grid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    articles.forEach(article => {
        const articleElement = createArticleElement(article);
        grid.appendChild(articleElement);
    });
    
    // Initialize animations after adding articles to the DOM
    initArticleAnimations();
}

// Create an article element
function createArticleElement(article) {
    const articleElement = document.createElement('article');
    articleElement.className = 'news-card';
    articleElement.dataset.id = article.id;
    
    articleElement.innerHTML = `
        <figure>
            <img src="${article.imageUrl}" alt="${article.title}">
            <figcaption>${article.title}</figcaption>
        </figure>
        <div class="card-content">
            <span class="category">${article.category}</span>
            <h3>${article.title}</h3>
            <div class="meta">
                By <span class="author">${article.author}</span> | 
                <span class="publish-time">${article.date}</span>
                <span class="read-time">${article.readTime}</span>
            </div>
            <p>${article.excerpt}</p>
            <a href="article.html?id=${article.id}" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
        </div>
    `;
    
    return articleElement;
}

// Load trending articles
function loadTrendingArticles() {
    // In a real application, this would be an API call
    const trendingArticles = generateMockArticles('trending', 5);
    const trendingList = document.getElementById('trending-articles');
    
    if (!trendingList) return;
    
    trendingList.innerHTML = '';
    
    trendingArticles.forEach((article, index) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <span class="trending-number">${index + 1}</span>
            <a href="article.html?id=${article.id}">
                <h4>${article.title}</h4>
                <span class="trending-category">${article.category}</span>
            </a>
        `;
        trendingList.appendChild(li);
    });
}

// Set up pagination
function setupPagination() {
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    const pageNumbers = document.querySelectorAll('.page-number');
    
    if (!prevButton || !nextButton) return;
    
    let currentPage = 1;
    const totalPages = 10; // In a real app, this would come from the API
    
    // Update pagination state
    function updatePagination() {
        // Update previous button
        prevButton.disabled = currentPage === 1;
        
        // Update next button
        nextButton.disabled = currentPage === totalPages;
        
        // Update page numbers
        pageNumbers.forEach((number, index) => {
            if (index + 1 === currentPage) {
                number.classList.add('active');
            } else {
                number.classList.remove('active');
            }
        });
    }
    
    // Event listeners
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            updatePagination();
            // In a real app, load the previous page of articles
        }
    });
    
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            updatePagination();
            // In a real app, load the next page of articles
        }
    });
    
    // Click handler for page numbers
    pageNumbers.forEach(number => {
        number.addEventListener('click', () => {
            currentPage = parseInt(number.textContent);
            updatePagination();
            // In a real app, load the selected page of articles
        });
    });
    
    // Initialize pagination
    updatePagination();
}

// Initialize animations for articles
function initArticleAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    document.querySelectorAll('.news-card').forEach(card => {
        observer.observe(card);
    });
}

// Set up event listeners
function setupEventListeners() {
    // Handle category filter changes
    const categoryLinks = document.querySelectorAll('.category-list a');
    categoryLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const category = link.getAttribute('href').replace('.html', '');
            // In a real app, this would update the URL and load the selected category
            console.log(`Category selected: ${category}`);
        });
    });
    
    // Handle newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-widget .subscribe-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const email = newsletterForm.querySelector('input[type="email"]').value.trim();
            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                newsletterForm.reset();
            }
        });
    }
}

// Show notification (reusing the function from main.js)
function showNotification(message, type = 'info') {
    // Check if the function exists in the global scope
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }
    
    // Fallback implementation
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Make the showNotification function available globally
window.showNotification = showNotification;
