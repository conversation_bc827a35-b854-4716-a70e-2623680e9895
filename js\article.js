// Article Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get the article ID from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const articleId = urlParams.get('id') || '1';
    
    // Load the article data
    loadArticle(articleId);
    
    // Load related articles
    loadRelatedArticles(articleId);
    
    // Load trending articles
    loadTrendingArticles();
    
    // Set up event listeners
    setupEventListeners();
    
    // Update the current date
    updateCurrentDate();
});

// Load article data
function loadArticle(articleId) {
    // In a real application, this would be an API call
    // For demonstration, we'll use mock data
    const mockArticle = generateMockArticle(articleId);
    
    // Update the page with article data
    updateArticlePage(mockArticle);
}

// Generate a mock article for demonstration
function generateMockArticle(articleId) {
    const categories = ['World', 'Politics', 'Business', 'Technology', 'Sports', 'Entertainment'];
    const authors = [
        { 
            name: '<PERSON>', 
            title: 'Senior Correspondent',
            bio: '<PERSON> has been covering international affairs for over 15 years, with a focus on climate change and environmental policy.',
            twitter: 'sarahj',
            linkedin: 'sarah<PERSON><PERSON><PERSON>',
            website: 'sarahjohnson.com'
        },
        { 
            name: 'Michael <PERSON>', 
            title: 'Technology Editor',
            bio: '<PERSON> specializes in covering the latest developments in artificial intelligence and emerging technologies.',
            twitter: 'michaelc',
            linkedin: 'michaelchen',
            website: 'michaelchen.tech'
        },
        { 
            name: 'Emma <PERSON>', 
            title: 'Political Analyst',
            bio: 'With a background in political science, Emma provides in-depth analysis of domestic and international politics.',
            twitter: 'emmad',
            linkedin: 'emmadavis',
            website: 'emmadavis.news'
        }
    ];
    
    const tags = [
        'Climate Change', 'Environment', 'Politics', 'Economy', 'Technology',
        'Health', 'Science', 'Education', 'Energy', 'Sustainability'
    ];
    
    // Get a random category and author
    const category = categories[Math.floor(Math.random() * categories.length)];
    const author = authors[Math.floor(Math.random() * authors.length)];
    
    // Generate a random date within the last month
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
    
    // Generate 3-5 random tags
    const articleTags = [];
    const numTags = Math.floor(Math.random() * 3) + 3;
    const availableTags = [...tags];
    
    for (let i = 0; i < numTags; i++) {
        if (availableTags.length === 0) break;
        const randomIndex = Math.floor(Math.random() * availableTags.length);
        articleTags.push(availableTags.splice(randomIndex, 1)[0]);
    }
    
    // Generate article content with multiple paragraphs and headings
    const paragraphs = [
        'In a significant development that could reshape the global economic landscape, world leaders have reached a historic agreement on climate change. The deal, which was finalized after weeks of intense negotiations, aims to reduce carbon emissions by 50% by 2030.',
        'The agreement comes at a critical time, as scientists warn that the world is rapidly approaching a point of no return in terms of climate change. "This is a make-or-break moment for our planet," said UN Secretary-General in his opening remarks at the summit.',
        'Under the terms of the agreement, developed nations have pledged to provide $100 billion annually to help developing countries transition to renewable energy sources and adapt to the impacts of climate change. This financial commitment has been a major sticking point in previous negotiations.',
        'The deal also includes provisions for regular reviews of each country\'s progress toward meeting their emissions targets. "We need transparency and accountability to ensure that all nations live up to their commitments," said the lead negotiator from the European Union.'
    ];
    
    // Add some subheadings
    paragraphs.splice(1, 0, '<h2>Why This Agreement Matters</h2>');
    paragraphs.splice(3, 0, '<h2>Financial Commitments and Accountability</h2>');
    
    // Add a blockquote
    const quotes = [
        '"The science is clear - we are the first generation to feel the impact of climate change and the last generation that can do something about it."',
        '"This agreement represents a turning point in our fight against climate change. The time for action is now."',
        '"We owe it to future generations to leave them a planet that is livable and sustainable. This deal is a step in the right direction."'
    ];
    
    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
    paragraphs.splice(2, 0, `<blockquote>${randomQuote}</blockquote>`);
    
    // Add an image
    const imageUrl = `https://source.unsplash.com/random/800x500/?${category.toLowerCase()},${articleId}`;
    paragraphs.splice(4, 0, `<figure><img src="${imageUrl}" alt="Related to ${category}"><figcaption>Image caption describing the photo above.</figcaption></figure>`);
    
    // Add a bulleted list
    const listItems = [
        'Key provisions of the agreement',
        'Timeline for implementation',
        'Financial commitments by country',
        'Mechanisms for monitoring progress'
    ];
    
    const listHtml = '<ul>' + listItems.map(item => `<li>${item}</li>`).join('') + '</ul>';
    paragraphs.push(`<h3>What\'s in the Agreement?</h3>${listHtml}`);
    
    // Final paragraph
    paragraphs.push('As the world moves forward with implementing this agreement, all eyes will be on the participating nations to ensure they follow through on their commitments. The success of this deal will depend on sustained political will and international cooperation in the years to come.');
    
    // Combine paragraphs into article body
    const articleBody = paragraphs.join('\n\n');
    
    return {
        id: articleId,
        title: `Global Leaders Reach Historic Climate Agreement in ${new Date().getFullYear()}`,
        excerpt: 'World leaders have reached a landmark agreement to combat climate change, with unprecedented commitments to reduce carbon emissions and transition to renewable energy sources.',
        content: articleBody,
        category: category,
        author: author.name,
        authorBio: author,
        date: date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
        imageUrl: imageUrl,
        imageCaption: 'World leaders gather for a group photo at the climate summit.',
        readTime: `${Math.floor(Math.random() * 8) + 5} min read`,
        tags: articleTags
    };
}

// Update the page with article data
function updateArticlePage(article) {
    // Update metadata
    document.title = `${article.title} - The Daily Chronicle`;
    
    // Update article header
    document.getElementById('article-category').textContent = article.category;
    document.getElementById('article-title').textContent = article.title;
    document.getElementById('article-author').textContent = article.author;
    document.getElementById('article-date').textContent = article.date;
    
    // Update featured image
    const articleImage = document.getElementById('article-image');
    if (articleImage) {
        articleImage.src = article.imageUrl;
        articleImage.alt = article.title;
    }
    
    // Update image caption
    const imageCaption = document.getElementById('image-caption');
    if (imageCaption) {
        imageCaption.textContent = article.imageCaption;
    }
    
    // Update article body
    const articleBody = document.getElementById('article-body');
    if (articleBody) {
        articleBody.innerHTML = article.content;
    }
    
    // Update author bio
    const authorBio = document.getElementById('author-bio');
    if (authorBio && article.authorBio) {
        authorBio.textContent = article.authorBio.bio;
        
        // Update author social links
        const twitterLink = document.querySelector('.author-social a[aria-label*="Twitter"]');
        const linkedinLink = document.querySelector('.author-social a[aria-label*="LinkedIn"]');
        const websiteLink = document.querySelector('.author-social a[aria-label*="Website"]');
        
        if (twitterLink) twitterLink.href = `https://twitter.com/${article.authorBio.twitter}`;
        if (linkedinLink) linkedinLink.href = `https://linkedin.com/in/${article.authorBio.linkedin}`;
        if (websiteLink) websiteLink.href = `https://${article.authorBio.website}`;
    }
    
    // Update tags
    const tagsContainer = document.getElementById('article-tags');
    if (tagsContainer) {
        tagsContainer.innerHTML = article.tags
            .map(tag => `<a href="#" class="tag">${tag}</a>`)
            .join('');
    }
    
    // Update share buttons
    const shareUrl = encodeURIComponent(window.location.href);
    const shareTitle = encodeURIComponent(article.title);
    const shareText = encodeURIComponent(article.excerpt);
    
    document.querySelectorAll('.social-share a').forEach(button => {
        let shareLink = '#';
        
        if (button.classList.contains('facebook')) {
            shareLink = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`;
        } else if (button.classList.contains('twitter')) {
            shareLink = `https://twitter.com/intent/tweet?url=${shareUrl}&text=${shareTitle}`;
        } else if (button.classList.contains('linkedin')) {
            shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}`;
        } else if (button.classList.contains('whatsapp')) {
            shareLink = `https://api.whatsapp.com/send?text=${shareTitle}%20${shareUrl}`;
        }
        
        button.href = shareLink;
    });
    
    // Set up copy link button
    const copyLinkButton = document.querySelector('.copy-link');
    if (copyLinkButton) {
        copyLinkButton.addEventListener('click', function(e) {
            e.preventDefault();
            navigator.clipboard.writeText(window.location.href).then(() => {
                // Show tooltip or notification
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = 'Link copied!';
                this.appendChild(tooltip);
                
                setTimeout(() => {
                    tooltip.remove();
                }, 2000);
            });
        });
    }
}

// Load related articles
function loadRelatedArticles(currentArticleId) {
    // In a real application, this would be an API call
    // For demonstration, we'll generate mock related articles
    const relatedArticles = [];
    const numRelated = 3;
    
    for (let i = 1; i <= numRelated; i++) {
        const id = (parseInt(currentArticleId) + i).toString();
        const article = generateMockArticle(id);
        relatedArticles.push(article);
    }
    
    // Update the related articles section
    const relatedContainer = document.getElementById('related-articles');
    if (relatedContainer) {
        relatedContainer.innerHTML = relatedArticles.map(article => `
            <article class="related-article">
                <a href="article.html?id=${article.id}" class="related-article-link">
                    <img src="${article.imageUrl}" alt="${article.title}">
                    <div class="related-article-content">
                        <span class="category">${article.category}</span>
                        <h4>${article.title}</h4>
                        <span class="read-time">${article.readTime}</span>
                    </div>
                </a>
            </article>
        `).join('');
    }
    
    // Update article navigation (previous/next)
    const prevArticle = generateMockArticle((parseInt(currentArticleId) - 1).toString());
    const nextArticle = generateMockArticle((parseInt(currentArticleId) + 1).toString());
    
    const prevButton = document.querySelector('.nav-button.previous');
    const nextButton = document.querySelector('.nav-button.next');
    
    if (prevButton) {
        prevButton.href = `article.html?id=${prevArticle.id}`;
        prevButton.querySelector('.nav-article-title').textContent = prevArticle.title;
    }
    
    if (nextButton) {
        nextButton.href = `article.html?id=${nextArticle.id}`;
        nextButton.querySelector('.nav-article-title').textContent = nextArticle.title;
    }
}

// Load trending articles
function loadTrendingArticles() {
    // In a real application, this would be an API call
    const trendingArticles = [];
    const numTrending = 5;
    
    for (let i = 1; i <= numTrending; i++) {
        const article = generateMockArticle(`trending-${i}`);
        trendingArticles.push(article);
    }
    
    // Update the trending articles section
    const trendingContainer = document.getElementById('trending-articles');
    if (trendingContainer) {
        trendingContainer.innerHTML = trendingArticles.map((article, index) => `
            <li>
                <span class="trending-number">${index + 1}</span>
                <a href="article.html?id=${article.id}">
                    <h4>${article.title}</h4>
                    <span class="trending-category">${article.category}</span>
                </a>
            </li>
        `).join('');
    }
}

// Update the current date in the header
function updateCurrentDate() {
    const dateElements = document.querySelectorAll('.date');
    if (dateElements.length > 0) {
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const today = new Date();
        const dateString = today.toLocaleDateString('en-US', options);
        dateElements.forEach(el => {
            el.textContent = dateString;
        });
    }
}

// Set up event listeners
function setupEventListeners() {
    // Handle newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-widget .subscribe-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value.trim();
            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                this.reset();
            }
        });
    }
    
    // Handle save article button
    const saveButton = document.querySelector('.action-button[aria-label="Save article"]');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            const icon = this.querySelector('i');
            
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('Article saved to your reading list', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('Article removed from your reading list', 'info');
            }
        });
    }
    
    // Handle print button
    const printButton = document.querySelector('.action-button[aria-label="Print article"]');
    if (printButton) {
        printButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Check if the function exists in the global scope (from main.js)
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }
    
    // Fallback implementation
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Make the showNotification function available globally
window.showNotification = showNotification;
