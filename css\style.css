/* Base Styles */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #8b0000;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --border-color: #e0e0e0;
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Source Serif Pro', serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f7;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h100v100H0z" fill="none"/><path d="M0 100 L100 0" stroke="rgba(0,0,0,0.05)" stroke-width="1"/></svg>');
}

.newspaper {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.masthead {
    padding: 1.5rem 2rem;
    border-bottom: 2px solid var(--primary-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
}

.weather, .date, .edition {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.masthead h1 {
    font-family: var(--font-heading);
    font-size: 4rem;
    text-align: center;
    margin: 1rem 0;
    color: var(--primary-color);
    letter-spacing: 1px;
    text-transform: uppercase;
    font-weight: 700;
}

/* Navigation */
.main-nav {
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0.8rem 0;
}

.main-nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--secondary-color);
}

/* Featured Story */
.featured-story {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.featured-story figure {
    margin: 0 0 1.5rem 0;
    position: relative;
}

.featured-story img {
    width: 100%;
    height: auto;
    display: block;
}

.featured-story figcaption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.category {
    display: inline-block;
    background: var(--secondary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.featured-story h2 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.meta .author {
    color: var(--secondary-color);
    font-weight: 600;
}

.featured-story p {
    margin-bottom: 1.2rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

.featured-story p.lead {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.read-more {
    display: inline-flex;
    align-items: center;
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    margin-top: 1rem;
    transition: color 0.3s ease;
}

.read-more i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.read-more:hover {
    color: #5c0000;
}

.read-more:hover i {
    transform: translateX(5px);
}

/* News Grid */
.news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem;
}

.news-card {
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 1.5rem;
}

.card-content .category {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    margin-bottom: 0.8rem;
}

.card-content h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
    line-height: 1.3;
}

.card-content p {
    color: #555;
    margin-bottom: 1.2rem;
    font-size: 0.95rem;
}

/* Newsletter */
.newsletter {
    background: var(--light-gray);
    padding: 3rem 2rem;
    text-align: center;
    margin: 2rem 0;
}

.newsletter h3 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.newsletter p {
    color: #666;
    margin-bottom: 1.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.subscribe-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.subscribe-form input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: 1px solid #ddd;
    border-right: none;
    font-family: inherit;
    font-size: 1rem;
}

.subscribe-form button {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0 2rem;
    font-family: inherit;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.subscribe-form button:hover {
    background: #6d0000;
}

/* Footer */
.site-footer {
    background: var(--primary-color);
    color: #ccc;
    padding: 3rem 2rem 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: white;
    font-family: var(--font-heading);
    font-size: 1.2rem;
    margin-bottom: 1.2rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background: var(--secondary-color);
}

.footer-section p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.6rem;
}

.footer-section a {
    color: #aaa;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: #888;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .masthead h1 {
        font-size: 3rem;
    }
    
    .main-nav ul {
        flex-wrap: wrap;
        gap: 1rem;
        padding: 0 1rem;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .featured-story {
        padding: 1.5rem;
    }
    
    .featured-story h2 {
        font-size: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .subscribe-form {
        flex-direction: column;
    }
    
    .subscribe-form input {
        border-right: 1px solid #ddd;
        border-bottom: none;
    }
    
    .subscribe-form button {
        padding: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .masthead h1 {
        font-size: 2.2rem;
    }
    
    .main-nav ul {
        gap: 0.5rem;
    }
    
    .main-nav a {
        font-size: 0.8rem;
    }
    
    .featured-story h2 {
        font-size: 1.6rem;
    }
    
    .featured-story p.lead {
        font-size: 1.1rem;
    }
}
